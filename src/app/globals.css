@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  
  /* LanguageGems colors */
  --teal-primary: 20, 184, 166;
  --teal-dark: 15, 118, 110;
  --coral-accent: 251, 113, 133;
  --yellow-accent: 250, 204, 21;
  --emerald-accent: 16, 185, 129;
  --purple-accent: 168, 85, 247;
  --orange-accent: 249, 115, 22;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
    to bottom,
    rgb(var(--background-start-rgb)),
    rgb(var(--background-end-rgb))
  );
  min-height: 100vh;
  font-family: Arial, Helvetica, sans-serif;
}

@keyframes bubbleRise {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 0.6;
  }
  100% {
    transform: translateY(-40px) scale(0.8);
    opacity: 0;
  }
}

@keyframes shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-3px); }
  50% { transform: translateX(3px); }
  75% { transform: translateX(-3px); }
  100% { transform: translateX(0); }
}

.animate-shake {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes fall {
  from {
    transform: translateY(-100vh);
    opacity: 1;
  }
  to {
    transform: translateY(100vh);
    opacity: 0.7;
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.7);
  }
}

@keyframes wave {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 200px 0;
  }
}

.animate-fall {
  animation: fall 3s ease-in forwards;
}

/* Additional animations for homepage */
@keyframes ping-slow {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes ping-delayed {
  10%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes ping-slow-delayed {
  20%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(45deg);
  }
  50% {
    transform: translateY(-20px) rotate(45deg);
  }
  100% {
    transform: translateY(0px) rotate(45deg);
  }
}

@keyframes float-delayed {
  0% {
    transform: translateY(0px) rotate(12deg);
  }
  50% {
    transform: translateY(-15px) rotate(12deg);
  }
  100% {
    transform: translateY(0px) rotate(12deg);
  }
}

@keyframes float-slow {
  0% {
    transform: translateY(0px) rotate(-12deg);
  }
  50% {
    transform: translateY(-10px) rotate(-12deg);
  }
  100% {
    transform: translateY(0px) rotate(-12deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -500px 0;
  }
  100% {
    background-position: 500px 0;
  }
}

@keyframes hover {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-ping-delayed {
  animation: ping-delayed 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-ping-slow-delayed {
  animation: ping-slow-delayed 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 5s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-shimmer {
  background-size: 1000px 100%;
  animation: shimmer 8s infinite linear;
}

.animate-hover {
  animation: hover 4s ease-in-out infinite;
}

/* Variations with different timings */
.animate-float-slow-delayed {
  animation: float 9s ease-in-out infinite;
}

.animate-hover-delayed {
  animation: hover 5s ease-in-out infinite;
}

.animate-hover-slow {
  animation: hover 6s ease-in-out infinite;
}

.animate-hover-slow-delayed {
  animation: hover 7s ease-in-out infinite;
}

/* Speech Bubble Styles */
.speech-bubble-right {
  position: relative;
}

.speech-bubble-right::after {
  content: '';
  position: absolute;
  right: 30px;
  bottom: -20px;
  border-width: 20px 20px 0;
  border-style: solid;
  border-color: inherit transparent;
  display: block;
  width: 0;
}

.speech-bubble-left {
  position: relative;
}

.speech-bubble-left::after {
  content: '';
  position: absolute;
  left: 30px;
  bottom: -20px;
  border-width: 20px 20px 0;
  border-style: solid;
  border-color: inherit transparent;
  display: block;
  width: 0;
}

@layer components {
  .gem-button {
    @apply relative inline-flex items-center justify-center px-8 py-3 overflow-hidden font-bold rounded-full 
    bg-gradient-to-r from-blue-400 to-cyan-300 text-white shadow-lg transition-all duration-300 
    hover:scale-105 hover:shadow-xl focus:outline-none;
  }
  
  .pink-gem-button {
    @apply relative inline-flex items-center justify-center px-8 py-3 overflow-hidden font-bold rounded-full 
    bg-gradient-to-r from-pink-500 to-fuchsia-400 text-white shadow-lg transition-all duration-300 
    hover:scale-105 hover:shadow-xl focus:outline-none;
  }
  
  .purple-gem-button {
    @apply relative inline-flex items-center justify-center px-8 py-3 overflow-hidden font-bold rounded-full 
    bg-gradient-to-r from-purple-500 to-indigo-400 text-white shadow-lg transition-all duration-300 
    hover:scale-105 hover:shadow-xl focus:outline-none;
  }
  
  .gem-nav-item {
    @apply px-4 py-2 text-sm font-medium text-white rounded-md hover:bg-white/25 transition-all duration-200;
  }
  
  .gem-glow {
    @apply relative;
  }
  
  .gem-glow::before {
    @apply content-[''] absolute -inset-1 bg-gradient-to-r from-cyan-400 to-fuchsia-400 rounded-full blur opacity-75 transition duration-300;
  }
  
  .gem-glow:hover::before {
    @apply opacity-100;
  }
  
  .text-gradient-blue-purple {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400;
  }
}

@layer utilities {
  .heading-font {
    font-family: var(--font-cinzel);
    font-weight: 700;
  }
  
  .body-font {
    font-family: var(--font-inter);
    font-weight: 400;
  }
  
  .teal-gradient {
    background: linear-gradient(135deg, rgb(var(--teal-primary)) 0%, rgb(209, 250, 229) 100%);
  }
  
  .dashboard-gradient {
    background: linear-gradient(135deg, rgb(209, 250, 229) 0%, rgb(254, 226, 226) 100%);
  }
  
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom animations for Gem Collector game */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes float-in {
  0% { opacity: 0; transform: translateX(100px) scale(0.8); }
  100% { opacity: 1; transform: translateX(0) scale(1); }
}

@keyframes scale-in {
  0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

@keyframes bounce-in {
  0% { transform: scale(0) rotate(180deg); opacity: 0; }
  50% { transform: scale(1.25) rotate(90deg); opacity: 0.8; }
  100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

@keyframes fade-in {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-in {
  animation: float-in 0.5s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-bounce-in {
  animation: bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}
