"use client";

import { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, Zap } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '../../../components/auth/AuthProvider';
import { useRouter, useSearchParams } from 'next/navigation';
import { SentenceTranslation, SentenceSegment, SentenceSegmentOption } from '../../api/games/gem-collector/sentences/route';
import GemCollectorSettings, { GameSettings } from '../../../components/games/GemCollectorSettings';

interface GemOption {
  id: string;
  text: string;
  isCorrect: boolean;
  lane: number; // 0, 1, or 2 (top, middle, bottom)
  position: number; // x position
  segmentId: string;
  explanation?: string;
}

interface GameSession {
  sessionId: string;
  startTime: Date;
  totalSegments: number;
  correctSegments: number;
  incorrectSegments: number;
  gemsCollected: number;
  speedBoostsUsed: number;
  segmentAttempts: SegmentAttempt[];
}

interface SegmentAttempt {
  segmentId: string;
  selectedOptionId: string;
  isCorrect: boolean;
  responseTime: number;
  gemsEarned: number;
}

interface GameMode {
  type: 'free_play' | 'assignment';
  assignmentId?: string;
  language: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  theme?: string;
  topic?: string;
}

export default function GemCollectorGame() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Game mode and configuration
  const [gameMode, setGameMode] = useState<GameMode>({
    type: 'free_play',
    language: 'spanish',
    difficulty: 'beginner'
  });

  // Game state
  const [sentences, setSentences] = useState<SentenceTranslation[]>([]);
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);
  const [playerLane, setPlayerLane] = useState(1); // 0=top, 1=middle, 2=bottom
  const [gems, setGems] = useState<GemOption[]>([]);
  const [score, setScore] = useState(0);
  const [gameSpeed, setGameSpeed] = useState(2);
  const [speedBoostActive, setSpeedBoostActive] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameOver, setGameOver] = useState(false);
  const [feedback, setFeedback] = useState<{ type: 'correct' | 'wrong' | null; text: string }>({ type: null, text: '' });
  const [lives, setLives] = useState(3);
  const [backgroundPosition, setBackgroundPosition] = useState(0);
  const [builtSentence, setBuiltSentence] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Session tracking
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [segmentStartTime, setSegmentStartTime] = useState<Date | null>(null);

  // Calculate derived state before any early returns
  const currentSentence = sentences[currentSentenceIndex];
  const currentSegment = currentSentence?.segments[currentSegmentIndex];
  const progressPercentage = sentences.length > 0
    ? ((currentSentenceIndex * 100) / sentences.length) + ((currentSegmentIndex * 100) / (sentences.length * (currentSentence?.segments.length || 1)))
    : 0;

  // Initialize game mode from URL parameters
  useEffect(() => {
    const assignmentId = searchParams?.get('assignment');
    const language = searchParams?.get('language') || 'spanish';
    const difficulty = searchParams?.get('difficulty') || 'beginner';
    const theme = searchParams?.get('theme') || undefined;
    const topic = searchParams?.get('topic') || undefined;

    setGameMode({
      type: assignmentId ? 'assignment' : 'free_play',
      assignmentId: assignmentId || undefined,
      language,
      difficulty: difficulty as 'beginner' | 'intermediate' | 'advanced',
      theme,
      topic
    });
  }, [searchParams]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth/login');
    }
  }, [user, isLoading, router]);

  // Fetch sentences when game mode is set
  useEffect(() => {
    if (gameMode && user && !loading) {
      fetchSentences();
    }
  }, [gameMode, user]);

  const fetchSentences = async () => {
    if (loading) return; // Prevent multiple simultaneous requests

    setLoading(true);
    console.log('Fetching sentences with gameMode:', gameMode);
    try {
      const response = await fetch('/api/games/gem-collector/sentences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: gameMode.type === 'assignment' ? 'assignment' : 'freeplay',
          assignmentId: gameMode.assignmentId,
          language: gameMode.language,
          difficulty: gameMode.difficulty,
          theme: gameMode.theme,
          topic: gameMode.topic,
          count: 10
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', response.status, errorData);
        console.log('Using fallback sentences due to API error');
        setSentences(getFallbackSentences());
        setLoading(false);
        return;
      }

      const data = await response.json();
      console.log('API Response data:', data);
      setSentences(data.sentences || []);
      if (!data.sentences || data.sentences.length === 0) {
        console.log('No sentences in API response, using fallback');
        setSentences(getFallbackSentences());
      }
    } catch (error) {
      console.error('Error fetching sentences:', error);
      // Use fallback sentences if API fails
      console.log('Using fallback sentences due to fetch error');
      setSentences(getFallbackSentences());
    } finally {
      setLoading(false);
    }
  };

  // Fallback sentences for when API fails
  const getFallbackSentences = (): SentenceTranslation[] => [
    {
      id: 'fallback-1',
      englishSentence: 'I like to go to the cinema',
      targetLanguage: gameMode.language,
      targetSentence: 'Me gusta ir al cine',
      difficultyLevel: gameMode.difficulty,
      theme: 'Leisure and entertainment',
      topic: 'Free time activities',
      grammarFocus: 'gustar-verb',
      curriculumTier: 'Foundation',
      wordCount: 6,
      complexityScore: 30,
      segments: [
        {
          id: 'fallback-seg-1',
          segmentOrder: 1,
          englishSegment: 'I like',
          targetSegment: 'Me gusta',
          segmentType: 'phrase',
          grammarNote: 'Gustar construction',
          options: [
            { id: 'opt-1', optionText: 'Me gusta', isCorrect: true, distractorType: 'semantic' },
            { id: 'opt-2', optionText: 'Me encanta', isCorrect: false, distractorType: 'semantic' },
            { id: 'opt-3', optionText: 'Odio', isCorrect: false, distractorType: 'semantic' }
          ]
        },
        {
          id: 'fallback-seg-2',
          segmentOrder: 2,
          englishSegment: 'to go',
          targetSegment: 'ir',
          segmentType: 'word',
          options: [
            { id: 'opt-4', optionText: 'ir', isCorrect: true, distractorType: 'semantic' },
            { id: 'opt-5', optionText: 'venir', isCorrect: false, distractorType: 'semantic' },
            { id: 'opt-6', optionText: 'estar', isCorrect: false, distractorType: 'grammatical' }
          ]
        },
        {
          id: 'fallback-seg-3',
          segmentOrder: 3,
          englishSegment: 'to the cinema',
          targetSegment: 'al cine',
          segmentType: 'phrase',
          grammarNote: 'Contraction al = a + el',
          options: [
            { id: 'opt-7', optionText: 'al cine', isCorrect: true, distractorType: 'semantic' },
            { id: 'opt-8', optionText: 'del cine', isCorrect: false, distractorType: 'grammatical' },
            { id: 'opt-9', optionText: 'en cine', isCorrect: false, distractorType: 'grammatical' }
          ]
        }
      ]
    }
  ];

  // Handle keyboard input
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    if (!gameStarted || gameOver) return;

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        setPlayerLane(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
        event.preventDefault();
        setPlayerLane(prev => Math.min(2, prev + 1));
        break;
      case 'ArrowRight':
        event.preventDefault();
        activateSpeedBoost();
        break;
    }
  }, [gameStarted, gameOver]);

  const activateSpeedBoost = () => {
    if (speedBoostActive) return;

    setSpeedBoostActive(true);
    setGameSpeed(prev => prev * 2);

    // Update session tracking
    if (gameSession) {
      setGameSession(prev => prev ? {
        ...prev,
        speedBoostsUsed: prev.speedBoostsUsed + 1
      } : null);
    }

    // Speed boost lasts for 3 seconds
    setTimeout(() => {
      setSpeedBoostActive(false);
      setGameSpeed(prev => prev / 2);
    }, 3000);
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  // Game loop
  useEffect(() => {
    if (!gameStarted || gameOver) return;

    const gameLoop = setInterval(() => {
      // Move background
      setBackgroundPosition(prev => prev - gameSpeed);

      // Move gems and check collisions
      setGems(prevGems => {
        const updatedGems = prevGems.map(gem => ({
          ...gem,
          position: gem.position - gameSpeed
        })).filter(gem => gem.position > -100);

        // Check for collisions
        const playerGem = updatedGems.find(gem =>
          gem.lane === playerLane &&
          gem.position >= 200 &&
          gem.position <= 300
        );

        if (playerGem) {
          handleGemCollection(playerGem);
          return updatedGems.filter(g => g.id !== playerGem.id);
        }

        return updatedGems;
      });
    }, 50);

    return () => clearInterval(gameLoop);
  }, [gameStarted, gameOver, playerLane, gameSpeed, currentSentenceIndex, currentSegmentIndex]);

  const handleGemCollection = (gem: GemOption) => {
    const responseTime = segmentStartTime ? Date.now() - segmentStartTime.getTime() : 0;

    // Record the attempt
    const attempt: SegmentAttempt = {
      segmentId: gem.segmentId,
      selectedOptionId: gem.id,
      isCorrect: gem.isCorrect,
      responseTime,
      gemsEarned: gem.isCorrect ? 10 : 0
    };

    // Update session tracking
    if (gameSession) {
      setGameSession(prev => prev ? {
        ...prev,
        totalSegments: prev.totalSegments + 1,
        correctSegments: prev.correctSegments + (gem.isCorrect ? 1 : 0),
        incorrectSegments: prev.incorrectSegments + (gem.isCorrect ? 0 : 1),
        gemsCollected: prev.gemsCollected + attempt.gemsEarned,
        segmentAttempts: [...prev.segmentAttempts, attempt]
      } : null);
    }

    if (gem.isCorrect) {
      // Add to built sentence
      const currentSentence = sentences[currentSentenceIndex];
      const currentSegment = currentSentence?.segments[currentSegmentIndex];

      if (currentSegment) {
        setBuiltSentence(prev => [...prev, currentSegment.targetSegment]);
        setScore(prev => prev + 100 + attempt.gemsEarned);
        setFeedback({
          type: 'correct',
          text: `Correct! "${currentSegment.targetSegment}" +${100 + attempt.gemsEarned} points`
        });

        // Move to next segment or sentence
        setTimeout(() => {
          moveToNextSegment();
          setFeedback({ type: null, text: '' });
        }, 1500);
      }
    } else {
      setLives(prev => {
        const newLives = prev - 1;
        if (newLives <= 0) {
          setGameOver(true);
        }
        return newLives;
      });
      setFeedback({
        type: 'wrong',
        text: gem.explanation || 'Wrong! Try again'
      });
      setTimeout(() => setFeedback({ type: null, text: '' }), 2000);
    }
  };

  const moveToNextSegment = () => {
    const currentSentence = sentences[currentSentenceIndex];
    if (!currentSentence) return;

    if (currentSegmentIndex < currentSentence.segments.length - 1) {
      // Move to next segment in current sentence
      setCurrentSegmentIndex(prev => prev + 1);
      setSegmentStartTime(new Date());
    } else {
      // Sentence completed, move to next sentence
      if (currentSentenceIndex < sentences.length - 1) {
        setCurrentSentenceIndex(prev => prev + 1);
        setCurrentSegmentIndex(0);
        setBuiltSentence([]);
        setGameSpeed(prev => prev + 0.1); // Gradually increase difficulty
        setSegmentStartTime(new Date());
      } else {
        // All sentences completed
        setGameOver(true);
      }
    }
  };

  // Generate gems for current segment
  useEffect(() => {
    if (!gameStarted || gameOver || sentences.length === 0) return;

    const generateGems = () => {
      const currentSentence = sentences[currentSentenceIndex];
      const currentSegment = currentSentence?.segments[currentSegmentIndex];

      if (!currentSegment || currentSegment.options.length === 0) return;

      // Shuffle options and take up to 3
      const shuffledOptions = [...currentSegment.options].sort(() => Math.random() - 0.5);
      const selectedOptions = shuffledOptions.slice(0, 3);

      // Ensure we have exactly one correct option
      const hasCorrect = selectedOptions.some(opt => opt.isCorrect);
      if (!hasCorrect) {
        const correctOption = currentSegment.options.find(opt => opt.isCorrect);
        if (correctOption) {
          selectedOptions[Math.floor(Math.random() * selectedOptions.length)] = correctOption;
        }
      }

      const spawnX = typeof window !== 'undefined' ? window.innerWidth + 100 : 800;

      const newGems: GemOption[] = selectedOptions.map((option, index) => ({
        id: option.id,
        text: option.optionText,
        isCorrect: option.isCorrect,
        lane: index,
        position: spawnX + (index * 120), // stagger slightly
        segmentId: currentSegment.id,
        explanation: option.explanation
      }));

      setGems(prev => [...prev, ...newGems]);
    };

    // Generate initial gems
    generateGems();

    // Generate new gems periodically
    const gemGenerator = setInterval(generateGems, 4000);

    return () => clearInterval(gemGenerator);
  }, [currentSentenceIndex, currentSegmentIndex, gameStarted, gameOver, sentences]);

  // Initialize game session when starting
  const initializeGameSession = () => {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const session: GameSession = {
      sessionId,
      startTime: new Date(),
      totalSegments: 0,
      correctSegments: 0,
      incorrectSegments: 0,
      gemsCollected: 0,
      speedBoostsUsed: 0,
      segmentAttempts: []
    };

    setGameSession(session);
    setSegmentStartTime(new Date());
  };

  const startGame = (customSettings?: GameSettings) => {
    if (gameMode.type === 'free_play' && !customSettings) {
      setShowSettings(true);
      return;
    }

    if (customSettings) {
      setGameMode(prev => ({
        ...prev,
        language: customSettings.language,
        difficulty: customSettings.difficulty,
        theme: customSettings.theme,
        topic: customSettings.topic
      }));
      setLives(customSettings.livesCount);
    }

    if (sentences.length === 0) {
      alert('No sentences available. Please try again later.');
      return;
    }

    setGameStarted(true);
    setGameOver(false);
    setScore(0);
    setCurrentSentenceIndex(0);
    setCurrentSegmentIndex(0);
    setPlayerLane(1);
    setGems([]);
    setGameSpeed(2);
    setSpeedBoostActive(false);
    setBuiltSentence([]);
    setFeedback({ type: null, text: '' });
    initializeGameSession();
  };

  const resetGame = () => {
    setGameStarted(false);
    setGameOver(false);
    setScore(0);
    setLives(3);
    setCurrentSentenceIndex(0);
    setCurrentSegmentIndex(0);
    setPlayerLane(1);
    setGems([]);
    setGameSpeed(2);
    setSpeedBoostActive(false);
    setBuiltSentence([]);
    setFeedback({ type: null, text: '' });
    setGameSession(null);
    setSegmentStartTime(null);
  };

  // Save game session when game ends
  const saveGameSession = async () => {
    if (!gameSession || !user) return;

    try {
      const response = await fetch('/api/games/gem-collector/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: gameSession.sessionId,
          assignmentId: gameMode.assignmentId,
          sessionType: gameMode.type,
          languagePair: `english_${gameMode.language}`,
          difficultyLevel: gameMode.difficulty,
          totalSentences: sentences.length,
          completedSentences: currentSentenceIndex + (currentSegmentIndex > 0 ? 1 : 0),
          totalSegments: gameSession.totalSegments,
          correctSegments: gameSession.correctSegments,
          incorrectSegments: gameSession.incorrectSegments,
          finalScore: score,
          gemsCollected: gameSession.gemsCollected,
          speedBoostsUsed: gameSession.speedBoostsUsed,
          segmentAttempts: gameSession.segmentAttempts,
          endedAt: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        console.error('Failed to save game session');
      }
    } catch (error) {
      console.error('Error saving game session:', error);
    }
  };

  // Save session when game ends
  useEffect(() => {
    if (gameOver && gameSession) {
      saveGameSession();
    }
  }, [gameOver, gameSession]);

  // Initialize sentences immediately on mount
  useEffect(() => {
    if (sentences.length === 0) {
      setSentences(getFallbackSentences());
    }
  }, []); // Empty dependency array to run only once on mount

  // --------------------------
  //   Render Conditions
  // --------------------------

  // Show loading spinner while authenticating or fetching sentences
  if (isLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white font-medium">
            {isLoading ? 'Loading game...' : 'Preparing sentences...'}
          </p>
        </div>
      </div>
    );
  }

  // If the user is not authenticated (a redirect will trigger shortly)
  if (!user) {
    return null;
  }

  if (!gameStarted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 overflow-hidden relative">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500/20 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-purple-500/20 rounded-full animate-bounce"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-500/20 rounded-full animate-ping"></div>
          <div className="absolute bottom-40 right-1/3 w-14 h-14 bg-cyan-500/20 rounded-full animate-pulse"></div>
        </div>

        <div className="flex items-center justify-center min-h-screen">
          <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-8 max-w-lg w-full mx-4 text-center shadow-2xl border border-white/20 animate-scale-in">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-6 animate-bounce">
              <span className="text-2xl">💎</span>
            </div>

            <h1 className="text-3xl font-bold text-slate-800 mb-4">
              ✨ Gem Collector ✨
              {gameMode.type === 'assignment' && (
                <span className="block text-lg text-blue-600 font-normal mt-1">📚 Assignment Mode</span>
              )}
            </h1>

            <p className="text-slate-600 mb-6 leading-relaxed">
              🎯 Build complete sentence translations word-by-word! Collect the correct translation gems
              in order to build each sentence. Use speed boost for extra challenge!
            </p>

            <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl p-4 mb-6 border border-slate-200">
              <h3 className="font-semibold text-slate-800 mb-3">🎮 How to Play:</h3>
              <ul className="text-sm text-slate-600 space-y-2 text-left">
                <li>• ⬆️ ⬇️ Use arrow keys to move between lanes</li>
                <li>• 🎯 Collect gems with correct translation segments in order</li>
                <li>• ⚡ Press → (right arrow) to activate speed boost</li>
                <li>• 🏆 Build complete sentences to earn bonus points</li>
                <li>• 💖 You have 3 lives - avoid wrong gems!</li>
              </ul>
            </div>

            {sentences.length > 0 && (
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-6 border border-blue-200">
                <h3 className="font-semibold text-blue-800 mb-2">🚀 Ready to Start:</h3>
                <div className="text-sm text-blue-600">
                  <p>📚 {sentences.length} sentences loaded</p>
                  <p>🌍 Language: {gameMode.language.charAt(0).toUpperCase() + gameMode.language.slice(1)}</p>
                  <p>📊 Difficulty: {gameMode.difficulty.charAt(0).toUpperCase() + gameMode.difficulty.slice(1)}</p>
                  {gameMode.theme && <p>🎯 Theme: {gameMode.theme}</p>}
                  {gameMode.topic && <p>📖 Topic: {gameMode.topic}</p>}
                </div>
              </div>
            )}

            <button
              onClick={() => startGame()}
              disabled={sentences.length === 0}
              className="w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-bold rounded-2xl px-8 py-4 text-lg shadow-xl hover:shadow-2xl transform transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {sentences.length === 0 ? '⏳ Loading Sentences...' :
               gameMode.type === 'assignment' ? '🎓 Start Assignment! 💎' : '⚙️ Configure & Start! 🎮'}
            </button>

            {/* Settings Modal (Configure & Start) */}
            <GemCollectorSettings
              isOpen={showSettings}
              onClose={() => setShowSettings(false)}
              onStartGame={(settings) => {
                const newGameMode = {
                  ...gameMode,
                  language: settings.language,
                  difficulty: settings.difficulty,
                  theme: settings.theme,
                  topic: settings.topic
                };
                setGameMode(newGameMode);

                // Fetch sentences with new settings, then start the game
                fetchSentences().then(() => {
                  startGame(settings);
                });
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 overflow-hidden relative">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500/20 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-purple-500/20 rounded-full animate-bounce"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-500/20 rounded-full animate-ping"></div>
        <div className="absolute bottom-40 right-1/3 w-14 h-14 bg-cyan-500/20 rounded-full animate-pulse"></div>
      </div>

      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-20 bg-black/30 backdrop-blur-lg border-b border-white/10">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <Link href="/games" className="text-white hover:text-cyan-300 transition-all duration-300 hover:scale-110">
            <ArrowLeft className="w-6 h-6 drop-shadow-lg" />
          </Link>

          <div className="flex items-center gap-6 text-white text-sm">
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2">
              <div className="text-xs opacity-80">Score</div>
              <div className="text-xl font-bold text-cyan-300">{score}</div>
            </div>
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2">
              <div className="text-xs opacity-80">Lives</div>
              <div className="text-lg font-bold flex items-center gap-1">
                {Array.from({ length: lives }).map((_, i) => (
                  <span key={i} className="text-red-400 animate-pulse">💖</span>
                ))}
              </div>
            </div>
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2">
              <div className="text-xs opacity-80">Progress</div>
              <div className="text-lg font-bold text-green-300">{currentSentenceIndex + 1}/{sentences.length}</div>
            </div>
            {speedBoostActive && (
              <div className="text-center bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-lg px-3 py-2 animate-pulse">
                <div className="text-xs opacity-80">Speed Boost</div>
                <div className="text-lg font-bold flex items-center gap-1">
                  <Zap className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 animate-bounce">ACTIVE</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="absolute top-20 left-0 right-0 z-20">
        <div className="container mx-auto px-4">
          <div className="bg-white/10 rounded-full h-3 shadow-lg">
            <div
              className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-500 h-3 rounded-full transition-all duration-500 shadow-lg animate-pulse"
              style={{ width: `${Math.min(100, progressPercentage)}%` }}
            />
          </div>
        </div>
      </div>

      {/* Current Sentence Building */}
      <div className="absolute top-28 left-0 right-0 z-20 text-center">
        <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-6 mx-4 shadow-2xl border border-white/20">
          {currentSentence && (
            <>
              <div className="text-lg text-gradient bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-bold mb-3">
                🌍 Translate to {currentSentence.targetLanguage.charAt(0).toUpperCase() + currentSentence.targetLanguage.slice(1)}
              </div>
              <div className="text-2xl font-bold text-slate-800 mb-4 leading-relaxed">
                "{currentSentence.englishSentence}"
              </div>

              {/* Built sentence so far */}
              <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl p-4 mb-4 border border-slate-200">
                <div className="text-sm text-slate-600 mb-2 font-medium">🔨 Building Translation:</div>
                <div className="text-xl font-bold text-slate-800 min-h-[32px] leading-relaxed">
                  {builtSentence.length > 0 ? (
                    <span className="animate-fade-in">{builtSentence.join(' ')}</span>
                  ) : (
                    <span className="text-slate-400 italic">Choose the correct words...</span>
                  )}
                  {currentSegment && (
                    <span className="text-blue-600 ml-2 font-semibold animate-pulse">
                      + "{currentSegment.englishSegment}"
                    </span>
                  )}
                </div>
              </div>

              {/* Current segment hint */}
              {currentSegment && (
                <div className="text-base text-slate-700 bg-blue-50 rounded-xl p-3">
                  <span className="font-bold text-blue-700">🎯 Next:</span> "{currentSegment.englishSegment}"
                  {currentSegment.grammarNote && (
                    <div className="text-sm text-blue-600 mt-2 font-medium">
                      💡 {currentSegment.grammarNote}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Game Area */}
      <div className="absolute inset-0 pt-64">
        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-white/30 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`
              }}
            />
          ))}
        </div>

        {/* Magical Selection Zones */}
        <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-80">
          <div className="h-full relative mx-8">
            {[0, 1, 2].map((lane) => (
              <div
                key={lane}
                className={`absolute left-0 right-0 h-1/3 border-2 border-dashed transition-all duration-300 rounded-2xl ${
                  playerLane === lane 
                    ? 'border-cyan-400 bg-cyan-500/10 shadow-lg shadow-cyan-500/20' 
                    : 'border-white/20 bg-white/5'
                }`}
                style={{ top: `${lane * 33.33}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent rounded-2xl"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Player Character */}
        <div 
          className="absolute left-16 transition-all duration-300 z-10"
          style={{
            top: `calc(50% - 120px + ${playerLane * 107}px)`,
          }}
        >
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-2xl animate-bounce">
              <span className="text-2xl animate-pulse">✨</span>
            </div>
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full animate-ping"></div>
          </div>
        </div>

        {/* Floating Word Options */}
        {gems.map((gem) => (
          <div
            key={gem.id}
            className="absolute transition-all duration-200 z-10 animate-float-in"
            style={{
              left: `${gem.position}px`,
              top: `calc(50% - 120px + ${gem.lane * 107}px)`,
            }}
          >
            <div className={`px-6 py-3 rounded-2xl font-bold shadow-2xl hover:scale-110 transition-all duration-300 text-base whitespace-nowrap cursor-pointer ${
              gem.isCorrect 
                ? 'bg-gradient-to-r from-emerald-400 via-green-500 to-emerald-600 text-white border-2 border-emerald-300 shadow-emerald-500/50' 
                : 'bg-gradient-to-r from-slate-400 via-gray-500 to-slate-600 text-white border-2 border-slate-300 shadow-slate-500/50'
            }`}>
              {gem.text}
              <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Feedback with Animation */}
      {feedback.type && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 animate-scale-in">
          <div className={`
            px-10 py-6 rounded-3xl font-bold text-3xl shadow-2xl border-4 transform transition-all backdrop-blur-lg
            ${feedback.type === 'correct'
              ? 'bg-gradient-to-r from-green-400 via-emerald-500 to-green-600 border-green-200 text-white shadow-green-500/50'
              : 'bg-gradient-to-r from-red-400 via-rose-500 to-red-600 border-red-200 text-white shadow-red-500/50'
            }
          `}>
            <div className="absolute inset-0 bg-white/10 rounded-3xl animate-pulse"></div>
            <div className="flex items-center justify-center">
              <span className="mr-3 text-4xl">
                {feedback.type === 'correct' ? '🎉' : '💫'}
              </span>
              {feedback.text}
            </div>
          </div>
        </div>
      )}

      {/* Combo/Streak Indicator */}
      {gameSession && gameSession.correctSegments > 2 && (
        <div className="absolute top-36 right-8 z-20 animate-bounce-in">
          <div className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white px-6 py-3 rounded-2xl shadow-2xl shadow-orange-500/50 border-2 border-yellow-300">
            <div className="text-sm font-bold">
              🔥 {gameSession.correctSegments} Streak!
            </div>
          </div>
        </div>
      )}

      {/* Game Over */}
      {gameOver && (
        <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-40">
          <div className="bg-white rounded-3xl p-10 max-w-2xl w-full mx-4 text-center shadow-2xl border border-gray-200 animate-scale-in">
            <div className="w-20 h-20 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-2xl animate-bounce">
              <span className="text-3xl">🏆</span>
            </div>
            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 text-6xl animate-bounce">🎊</div>

            <h2 className="text-3xl font-bold text-slate-800 mb-4">
              {currentSentenceIndex >= sentences.length - 1 ? 'Congratulations!' : 'Game Over!'}
            </h2>

            <div className="bg-slate-50 rounded-xl p-4 mb-6">
              <div className="text-2xl font-bold text-blue-600 mb-2">Final Score</div>
              <div className="text-4xl font-bold text-slate-800">{score}</div>

              <div className="grid grid-cols-2 gap-4 mt-4 text-sm">
                <div className="text-center">
                  <div className="text-slate-600">Sentences</div>
                  <div className="font-bold text-slate-800">
                    {currentSentenceIndex + (currentSegmentIndex > 0 ? 1 : 0)} / {sentences.length}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-slate-600">Accuracy</div>
                  <div className="font-bold text-slate-800">
                    {gameSession ? Math.round((gameSession.correctSegments / Math.max(1, gameSession.totalSegments)) * 100) : 0}%
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-slate-600">Gems Collected</div>
                  <div className="font-bold text-slate-800">{gameSession?.gemsCollected || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-slate-600">Speed Boosts</div>
                  <div className="font-bold text-slate-800">{gameSession?.speedBoostsUsed || 0}</div>
                </div>
              </div>
            </div>

            {gameMode.type === 'assignment' && (
              <div className="bg-blue-50 rounded-xl p-4 mb-6">
                <div className="text-base text-blue-800 font-medium">
                  📚 Assignment completed! Your progress has been saved.
                </div>
              </div>
            )}

            <div className="flex gap-4">
              <button
                onClick={resetGame}
                className="flex-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-bold rounded-2xl px-8 py-4 shadow-xl hover:shadow-2xl transform transition-all hover:scale-105 text-lg"
              >
                🎮 Play Again
              </button>
              <Link
                href="/games"
                className="flex-1 bg-gradient-to-r from-slate-200 to-gray-300 text-slate-700 font-bold rounded-2xl px-8 py-4 text-center hover:from-slate-300 hover:to-gray-400 transition-all transform hover:scale-105 text-lg"
              >
                🏠 Back to Games
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
        <div className="bg-black/60 backdrop-blur-lg rounded-2xl px-6 py-3 text-white text-base text-center shadow-2xl border border-white/20">
          <div className="font-semibold">⬆️ ⬇️ Move Between Lanes • ➡️ Speed Boost</div>
          {speedBoostActive && (
            <div className="text-yellow-400 text-sm mt-2 animate-pulse font-bold">
              ⚡ SPEED BOOST ACTIVE! ⚡
            </div>
          )}
        </div>
      </div>
    </div>
  );
}